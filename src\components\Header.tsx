import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  ChevronDown,
  Globe,
  Menu,
  Home,
  Info,
  Building2,
  MessageCircle,
  Building,
  LogIn,
  UserPlus,
  X,
  User,
  LogOut,
  Settings,
} from "lucide-react";
import logoImage from "@/assets/logo.png";
import { Link } from "react-router-dom";
import LoginModal from "./LoginModal";

const Header = () => {
  const { t } = useTranslation();
  const { language, changeLanguage, isRTL } = useLanguage();
  const { user, isAuthenticated, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [loginModalTab, setLoginModalTab] = useState<"login" | "register">(
    "login"
  );

  const mainNavItems = [
    { key: "home", icon: Home, link: "/" },
    { key: "corporateStay", icon: Building },
    { key: "contact", icon: MessageCircle },
  ];

  return (
    <header className="w-full bg-white/95 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center group cursor-pointer">
              <img
                src={logoImage}
                alt="Hala Logo"
                className="w-12 h-12 object-contain transition-transform duration-300 group-hover:scale-105"
              />
              <span className="ml-2 text-xl font-bold text-gray-900 hidden sm:block">
                Hala
              </span>
            </Link>
          </div>

          {/* Navigation - Desktop */}
          <nav className="hidden lg:flex items-center space-x-1">
            {mainNavItems.map(({ key, icon: Icon, link }) => (
              <Link key={key} to={link || "#"}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-700 hover:text-[#279fc7] hover:bg-[#279fc7]/10 px-3 py-2 rounded-lg transition-colors"
                >
                  <Icon className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {t(`nav.${key}`)}
                </Button>
              </Link>
            ))}

            {/* About Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-700 hover:text-[#279fc7] hover:bg-[#279fc7]/10 px-3 py-2 rounded-lg transition-colors"
                >
                  <Info className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {t("nav.about")}
                  <ChevronDown
                    className={`h-3 w-3 ${isRTL ? "mr-1" : "ml-1"}`}
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-48 bg-white border border-gray-200 shadow-lg rounded-lg z-50">
                <DropdownMenuItem className="flex items-center hover:bg-gray-50 cursor-pointer">
                  <Building2
                    className={`h-4 w-4 ${
                      isRTL ? "ml-2" : "mr-2"
                    } text-gray-500`}
                  />
                  {t("nav.ourStory")}
                </DropdownMenuItem>
                <DropdownMenuItem className="flex items-center hover:bg-gray-50 cursor-pointer">
                  <Info
                    className={`h-4 w-4 ${
                      isRTL ? "ml-2" : "mr-2"
                    } text-gray-500`}
                  />
                  {t("nav.howItWorks")}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="flex items-center hover:bg-gray-50 cursor-pointer">
                  <Building
                    className={`h-4 w-4 ${
                      isRTL ? "ml-2" : "mr-2"
                    } text-gray-500`}
                  />
                  {t("nav.careers")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-2">
            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-700 hover:text-[#279fc7] hover:bg-[#279fc7]/10 px-3 py-2 rounded-lg transition-colors"
                >
                  <Globe className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  <span className="hidden sm:inline">
                    {language === "ar" ? "العربية" : "English"}
                  </span>
                  <ChevronDown
                    className={`h-3 w-3 ${isRTL ? "mr-1" : "ml-1"}`}
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-white border border-gray-200 shadow-lg rounded-lg z-50">
                <DropdownMenuItem
                  onClick={() => changeLanguage("en")}
                  className="hover:bg-gray-50 cursor-pointer"
                >
                  🇺🇸 English
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => changeLanguage("ar")}
                  className="hover:bg-gray-50 cursor-pointer"
                >
                  🇸🇦 العربية
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Auth Buttons - Desktop */}
            <div className="hidden md:flex items-center space-x-2">
              {isAuthenticated ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-700 hover:text-[#279fc7] hover:bg-[#279fc7]/10 px-3 py-2 rounded-lg transition-colors"
                    >
                      <div className="flex items-center space-x-2">
                        {user?.avatar ? (
                          <img
                            src={user.avatar}
                            alt={user.name}
                            className="w-6 h-6 rounded-full object-cover"
                          />
                        ) : (
                          <User className="h-4 w-4" />
                        )}
                        <span className="hidden sm:inline">{user?.name}</span>
                        <ChevronDown className="h-3 w-3" />
                      </div>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-48 bg-white border border-gray-200 shadow-lg rounded-lg z-50">
                    <Link to="/profile">
                      <DropdownMenuItem className="flex items-center hover:bg-gray-50 cursor-pointer">
                        <User
                          className={`h-4 w-4 ${
                            isRTL ? "ml-2" : "mr-2"
                          } text-gray-500`}
                        />
                        {t("auth.profile")}
                      </DropdownMenuItem>
                    </Link>
                    <DropdownMenuItem className="flex items-center hover:bg-gray-50 cursor-pointer">
                      <Settings
                        className={`h-4 w-4 ${
                          isRTL ? "ml-2" : "mr-2"
                        } text-gray-500`}
                      />
                      {t("auth.settings")}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={logout}
                      className="flex items-center hover:bg-gray-50 cursor-pointer text-red-600"
                    >
                      <LogOut
                        className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`}
                      />
                      {t("auth.signOut")}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-700 hover:text-[#279fc7] hover:bg-[#279fc7]/10 px-3 py-2 rounded-lg transition-colors"
                    onClick={() => {
                      setLoginModalTab("login");
                      setIsLoginModalOpen(true);
                    }}
                  >
                    <LogIn className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                    {t("nav.login")}
                  </Button>
                  <Button
                    size="sm"
                    className="bg-[#279fc7] hover:bg-[#279fc7]/90 text-white px-4 py-2 rounded-lg shadow-sm transition-colors"
                    onClick={() => {
                      setLoginModalTab("register");
                      setIsLoginModalOpen(true);
                    }}
                  >
                    <UserPlus
                      className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`}
                    />
                    {t("nav.signup")}
                  </Button>
                </>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="lg:hidden">
              <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-2">
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent className="w-80 bg-white border-gray-200 p-0">
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <img
                          src={logoImage}
                          alt="Hala Logo"
                          className="w-10 h-10 object-contain"
                        />
                        <span className="ml-3 text-lg font-bold text-gray-900">
                          Hala
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="p-2 hover:bg-gray-100 rounded-lg"
                      >
                        <X className="h-5 w-5" />
                      </Button>
                    </div>
                  </div>

                  <nav className="flex flex-col p-6 space-y-1">
                    {mainNavItems.map(({ key, icon: Icon, link }) => (
                      <Link key={key} to={link || "#"}>
                        <Button
                          variant="ghost"
                          className="w-full justify-start text-left py-3 px-4 hover:bg-gray-50 rounded-lg transition-colors"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          <Icon
                            className={`h-5 w-5 ${
                              isRTL ? "ml-3" : "mr-3"
                            } text-gray-500`}
                          />
                          <span className="text-gray-700">
                            {t(`nav.${key}`)}
                          </span>
                        </Button>
                      </Link>
                    ))}

                    <Button
                      variant="ghost"
                      className="w-full justify-start text-left py-3 px-4 hover:bg-gray-50 rounded-lg transition-colors"
                    >
                      <Info
                        className={`h-5 w-5 ${
                          isRTL ? "ml-3" : "mr-3"
                        } text-gray-500`}
                      />
                      <span className="text-gray-700">{t("nav.about")}</span>
                    </Button>

                    <div className="pt-4 mt-4 border-t border-gray-200 space-y-2">
                      {isAuthenticated ? (
                        <>
                          <div className="flex items-center px-4 py-3">
                            {user?.avatar ? (
                              <img
                                src={user.avatar}
                                alt={user.name}
                                className="w-8 h-8 rounded-full object-cover"
                              />
                            ) : (
                              <User className="h-5 w-5 text-gray-500" />
                            )}
                            <div className="ml-3">
                              <p className="text-sm font-medium text-gray-900">
                                {user?.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                {user?.email}
                              </p>
                            </div>
                          </div>
                          <Link to="/profile">
                            <Button
                              variant="ghost"
                              className="w-full justify-start text-left py-3 px-4 hover:bg-gray-50 rounded-lg transition-colors"
                              onClick={() => setIsMobileMenuOpen(false)}
                            >
                              <User
                                className={`h-5 w-5 ${
                                  isRTL ? "ml-3" : "mr-3"
                                } text-gray-500`}
                              />
                              <span className="text-gray-700">
                                {t("auth.profile")}
                              </span>
                            </Button>
                          </Link>
                          <Button
                            variant="ghost"
                            className="w-full justify-start text-left py-3 px-4 hover:bg-gray-50 rounded-lg transition-colors"
                          >
                            <Settings
                              className={`h-5 w-5 ${
                                isRTL ? "ml-3" : "mr-3"
                              } text-gray-500`}
                            />
                            <span className="text-gray-700">
                              {t("auth.settings")}
                            </span>
                          </Button>
                          <Button
                            onClick={logout}
                            variant="ghost"
                            className="w-full justify-start text-left py-3 px-4 hover:bg-red-50 rounded-lg transition-colors text-red-600"
                          >
                            <LogOut
                              className={`h-5 w-5 ${isRTL ? "ml-3" : "mr-3"}`}
                            />
                            <span>{t("auth.signOut")}</span>
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            variant="ghost"
                            className="w-full justify-start text-left py-3 px-4 hover:bg-gray-50 rounded-lg transition-colors"
                            onClick={() => {
                              setLoginModalTab("login");
                              setIsLoginModalOpen(true);
                              setIsMobileMenuOpen(false);
                            }}
                          >
                            <LogIn
                              className={`h-5 w-5 ${
                                isRTL ? "ml-3" : "mr-3"
                              } text-gray-500`}
                            />
                            <span className="text-gray-700">
                              {t("nav.login")}
                            </span>
                          </Button>
                          <Button
                            className="w-full bg-[#279fc7] hover:bg-[#279fc7]/90 text-white py-3 rounded-lg shadow-sm transition-colors"
                            onClick={() => {
                              setLoginModalTab("register");
                              setIsLoginModalOpen(true);
                              setIsMobileMenuOpen(false);
                            }}
                          >
                            <UserPlus
                              className={`h-5 w-5 ${isRTL ? "ml-2" : "mr-2"}`}
                            />
                            {t("nav.signup")}
                          </Button>
                        </>
                      )}
                    </div>
                  </nav>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        defaultTab={loginModalTab}
      />
    </header>
  );
};

export default Header;
