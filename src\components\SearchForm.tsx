import { useState } from "react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Calendar as CalendarIcon,
  Users,
  Search,
  Plus,
  Minus,
} from "lucide-react";
import { cn } from "@/lib/utils";

const SearchForm = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [activeField, setActiveField] = useState<string | null>(null);
  const [checkIn, setCheckIn] = useState<Date>();
  const [checkOut, setCheckOut] = useState<Date>();
  const [guests, setGuests] = useState(1);

  return (
    <Card className="w-full max-w-5xl mx-auto bg-white/95 backdrop-blur-xl shadow-lg border-0 rounded-xl overflow-hidden">
      <div className="p-3">
        <div className="flex flex-col lg:flex-row lg:divide-x divide-gray-200 gap-3 lg:gap-0">
          {/* Check-in Date */}
          <div className="flex-1 group px-4 py-3 hover:bg-[#279fc7]/5 transition-all duration-200 rounded-lg lg:rounded-none">
            <div className="text-[10px] font-medium text-gray-500 uppercase tracking-wide mb-1.5">
              {t("search.checkIn")}
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full h-auto p-0 justify-start text-left font-normal hover:bg-transparent transition-transform",
                    !checkIn && "text-gray-500"
                  )}
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className={`p-1.5 rounded-md bg-[#279fc7]/15 group-hover:bg-[#279fc7]/25 transition-colors ${
                        isRTL ? "ml-2" : ""
                      }`}
                    >
                      <CalendarIcon className="h-4 w-4 text-[#279fc7]" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900 leading-tight">
                        {checkIn
                          ? format(checkIn, "MMM dd")
                          : t("search.selectDate")}
                      </div>
                      {checkIn && (
                        <div className="text-[10px] text-gray-400">
                          {format(checkIn, "yyyy")}
                        </div>
                      )}
                    </div>
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto p-0 bg-white border border-gray-200 shadow-2xl rounded-2xl z-50 overflow-hidden"
                align="start"
              >
                <Calendar
                  mode="single"
                  selected={checkIn}
                  onSelect={setCheckIn}
                  disabled={(date) => date < new Date()}
                  initialFocus
                  className="pointer-events-auto"
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Check-out Date */}
          <div className="flex-1 group px-4 py-3 hover:bg-[#279fc7]/5 transition-all duration-200 rounded-lg lg:rounded-none">
            <div className="text-[10px] font-medium text-gray-500 uppercase tracking-wide mb-1.5">
              {t("search.checkOut")}
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  className={cn(
                    "w-full h-auto p-0 justify-start text-left font-normal hover:bg-transparent transition-transform",
                    !checkOut && "text-gray-500"
                  )}
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className={`p-1.5 rounded-md bg-[#279fc7]/15 group-hover:bg-[#279fc7]/25 transition-colors ${
                        isRTL ? "ml-2" : ""
                      }`}
                    >
                      <CalendarIcon className="h-4 w-4 text-[#279fc7]" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900 leading-tight">
                        {checkOut
                          ? format(checkOut, "MMM dd")
                          : t("search.selectDate")}
                      </div>
                      {checkOut && (
                        <div className="text-[10px] text-gray-400">
                          {format(checkOut, "yyyy")}
                        </div>
                      )}
                    </div>
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto p-0 bg-white border border-gray-200 shadow-2xl rounded-2xl z-50 overflow-hidden"
                align="start"
              >
                <Calendar
                  mode="single"
                  selected={checkOut}
                  onSelect={setCheckOut}
                  disabled={(date) =>
                    date < new Date() || (checkIn && date <= checkIn)
                  }
                  initialFocus
                  className="pointer-events-auto"
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Guests */}
          <div className="flex-1 group px-4 py-3 hover:bg-[#279fc7]/5 transition-all duration-200 rounded-lg lg:rounded-none">
            <div className="text-[10px] font-medium text-gray-500 uppercase tracking-wide mb-1.5">
              {t("search.guests")}
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full h-auto p-0 justify-start hover:bg-transparent transition-transform"
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className={`p-1.5 rounded-md bg-[#279fc7]/15 group-hover:bg-[#279fc7]/25 transition-colors ${
                        isRTL ? "ml-2" : ""
                      }`}
                    >
                      <Users className="h-4 w-4 text-[#279fc7]" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900 leading-tight">
                        {guests}{" "}
                        {guests === 1
                          ? t("search.guest")
                          : t("search.guestsPlural")}
                      </div>
                      <div className="text-[10px] text-gray-400">
                        اختر عدد النزلاء
                      </div>
                    </div>
                  </div>
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-72 p-6 bg-white border border-gray-200 shadow-xl rounded-xl z-50"
                align="start"
              >
                <div className="flex items-center justify-between">
                  <span className="text-base font-semibold text-gray-900">
                    {t("search.guests")}
                  </span>
                  <div className="flex items-center space-x-4">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-10 w-10 rounded-full border-gray-300 hover:bg-[#279fc7]/10 hover:border-[#279fc7]"
                      onClick={() => setGuests(Math.max(1, guests - 1))}
                      disabled={guests <= 1}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    <span className="w-12 text-center text-lg font-semibold text-gray-900">
                      {guests}
                    </span>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-10 w-10 rounded-full border-gray-300 hover:bg-[#279fc7]/10 hover:border-[#279fc7]"
                      onClick={() => setGuests(Math.min(10, guests + 1))}
                      disabled={guests >= 10}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Search Button */}
          <div className="flex-shrink-0 px-4 py-3 flex items-center justify-center">
            <Button className="px-6 py-2 h-auto bg-gradient-to-r from-[#279fc7] to-[#1f8bb3] hover:from-[#1f8bb3] hover:to-[#1a7a99] text-white shadow-md transition-all duration-300 hover:shadow-lg font-medium text-sm rounded-lg border-0">
              <Search className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
              {t("search.search")}
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default SearchForm;
