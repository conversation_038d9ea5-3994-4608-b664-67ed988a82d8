import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import FeaturesSection from '../FeaturesSection';

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

describe('FeaturesSection', () => {
  it('should render all tab buttons', () => {
    render(<FeaturesSection />);
    
    expect(screen.getByText('All Features')).toBeInTheDocument();
    expect(screen.getByText('Booking')).toBeInTheDocument();
    expect(screen.getByText('Support')).toBeInTheDocument();
    expect(screen.getByText('Amenities')).toBeInTheDocument();
    expect(screen.getByText('Payment')).toBeInTheDocument();
    expect(screen.getByText('Policies')).toBeInTheDocument();
    expect(screen.getByText('Management')).toBeInTheDocument();
    expect(screen.getByText('Location')).toBeInTheDocument();
  });

  it('should have "All Features" selected by default', () => {
    render(<FeaturesSection />);
    
    const allFeaturesButton = screen.getByText('All Features');
    expect(allFeaturesButton).toHaveAttribute('aria-selected', 'true');
  });

  it('should switch tabs when clicked', () => {
    render(<FeaturesSection />);
    
    const bookingButton = screen.getByText('Booking');
    const allFeaturesButton = screen.getByText('All Features');
    
    // Initially "All Features" should be selected
    expect(allFeaturesButton).toHaveAttribute('aria-selected', 'true');
    expect(bookingButton).toHaveAttribute('aria-selected', 'false');
    
    // Click on "Booking" tab
    fireEvent.click(bookingButton);
    
    // Now "Booking" should be selected
    expect(bookingButton).toHaveAttribute('aria-selected', 'true');
    expect(allFeaturesButton).toHaveAttribute('aria-selected', 'false');
  });

  it('should allow switching between multiple tabs', () => {
    render(<FeaturesSection />);
    
    const supportButton = screen.getByText('Support');
    const amenitiesButton = screen.getByText('Amenities');
    
    // Click on "Support" tab
    fireEvent.click(supportButton);
    expect(supportButton).toHaveAttribute('aria-selected', 'true');
    
    // Click on "Amenities" tab
    fireEvent.click(amenitiesButton);
    expect(amenitiesButton).toHaveAttribute('aria-selected', 'true');
    expect(supportButton).toHaveAttribute('aria-selected', 'false');
  });

  it('should support keyboard navigation', () => {
    render(<FeaturesSection />);
    
    const bookingButton = screen.getByText('Booking');
    
    // Focus the button and press Enter
    bookingButton.focus();
    fireEvent.keyDown(bookingButton, { key: 'Enter' });
    
    expect(bookingButton).toHaveAttribute('aria-selected', 'true');
  });
});
