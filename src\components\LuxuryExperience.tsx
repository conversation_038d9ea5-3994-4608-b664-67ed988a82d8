import { Check, Play, X } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { useState } from "react";
import LuxuryVideo from "@/assets/Luxury .mp4";

const LuxuryExperience = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  const features = [
    "luxuryExperience.features.concierge",
    "luxuryExperience.features.amenities",
    "luxuryExperience.features.dining",
    "luxuryExperience.features.spa",
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-800 via-blue-900 to-slate-700 bg-clip-text text-transparent mb-4">
                {t("luxuryExperience.title")}
              </h2>
              <h3
                className="text-2xl md:text-3xl font-semibold mb-6"
                style={{ color: "#279fc7" }}
              >
                {t("luxuryExperience.subtitle")}
              </h3>
              <p className="text-lg text-slate-600 leading-relaxed">
                {t("luxuryExperience.description")}
              </p>
            </div>

            <div className="space-y-4">
              {features.map((featureKey, index) => (
                <div
                  key={index}
                  className={`flex items-center ${
                    isRTL ? "space-x-reverse space-x-3" : "space-x-3"
                  }`}
                >
                  <div
                    className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm"
                    style={{ backgroundColor: "#279fc7" }}
                  >
                    <Check className="w-4 h-4 text-white" strokeWidth={2} />
                  </div>
                  <span className="text-slate-700 font-medium">
                    {t(featureKey)}
                  </span>
                </div>
              ))}
            </div>

            <button
              onClick={() => setIsVideoModalOpen(true)}
              className={`group inline-flex items-center bg-slate-800 hover:bg-slate-700 text-white px-6 py-3 rounded-2xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 ${
                isRTL ? "space-x-reverse space-x-3" : "space-x-3"
              }`}
            >
              <span className="font-medium text-sm">
                {t("luxuryExperience.watchVideo")}
              </span>
              <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center ml-2">
                <Play className="w-3 h-3 text-slate-800 group-hover:scale-110 transition-transform duration-300" />
              </div>
            </button>
          </div>

          <div className="relative">
            <div className="relative overflow-hidden rounded-2xl shadow-2xl">
              <img
                alt={t("luxuryExperience.imageAlt")}
                className="w-full h-96 lg:h-[500px] object-cover"
                src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
              />
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                <button
                  onClick={() => setIsVideoModalOpen(true)}
                  className="group w-20 h-20 bg-white/95 backdrop-blur-sm rounded-full flex items-center justify-center shadow-xl hover:bg-white hover:scale-110 transition-all duration-300 border border-white/20"
                >
                  <Play className="w-8 h-8 text-slate-700 ml-1 group-hover:scale-110 transition-transform duration-300" />
                </button>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-400/20 rounded-full blur-xl"></div>
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-slate-400/20 rounded-full blur-xl"></div>
          </div>
        </div>
      </div>

      {/* Video Modal */}
      {isVideoModalOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm animate-in fade-in duration-300"
          onClick={() => setIsVideoModalOpen(false)}
        >
          <div
            className="relative w-full max-w-4xl mx-4 animate-in zoom-in-95 slide-in-from-bottom-4 duration-300"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <button
              onClick={() => setIsVideoModalOpen(false)}
              className="absolute -top-12 right-0 w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-all duration-300 hover:scale-110 hover:rotate-90 animate-in slide-in-from-top-2 duration-500 delay-150"
            >
              <X className="w-6 h-6" />
            </button>

            {/* Video container */}
            <div className="relative w-full aspect-video bg-slate-800 rounded-2xl overflow-hidden shadow-2xl transform transition-all duration-300 hover:shadow-3xl hover:scale-[1.02]">
              <video
                className="w-full h-full object-cover"
                controls
                autoPlay
                muted
                playsInline
              >
                <source src={LuxuryVideo} type="video/mp4" />
                <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mb-4">
                    <Play className="w-8 h-8 text-white ml-1" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">
                    Your browser does not support the video tag
                  </h3>
                  <p className="text-white/70 text-center max-w-md">
                    Please update your browser to view this video
                  </p>
                </div>
              </video>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default LuxuryExperience;
