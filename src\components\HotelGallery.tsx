import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useTranslation } from "react-i18next";

const HotelGallery = () => {
  const { t } = useTranslation();

  const galleryItems = [
    {
      titleKey: "hotelGallery.facilities.grandLobby",
      image:
        "https://images.unsplash.com/photo-1564501049412-61c2a3083791?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "hotelGallery.facilities.grandLobby",
    },
    {
      titleKey: "hotelGallery.facilities.fineDining",
      image:
        "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "hotelGallery.facilities.fineDining",
    },
    {
      titleKey: "hotelGallery.facilities.luxurySpa",
      image:
        "https://images.unsplash.com/photo-1571902943202-507ec2618e8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "hotelGallery.facilities.luxurySpa",
    },
    {
      titleKey: "hotelGallery.facilities.infinityPool",
      image:
        "https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "hotelGallery.facilities.infinityPool",
    },
    {
      titleKey: "hotelGallery.facilities.fitnessCenter",
      image:
        "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "hotelGallery.facilities.fitnessCenter",
    },
    {
      titleKey: "hotelGallery.facilities.premiumSuites",
      image:
        "https://images.unsplash.com/photo-1611892440504-42a792e24d32?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "hotelGallery.facilities.premiumSuites",
    },
    {
      titleKey: "hotelGallery.facilities.conferenceHalls",
      image:
        "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "hotelGallery.facilities.conferenceHalls",
    },
    {
      titleKey: "hotelGallery.facilities.gardenTerrace",
      image:
        "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "hotelGallery.facilities.gardenTerrace",
    },
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-primary mb-4">
            {t("hotelGallery.title")}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t("hotelGallery.description")}
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {galleryItems.map((item, index) => (
            <div key={index} className="group cursor-pointer">
              <div className="relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="aspect-w-4 aspect-h-5">
                  <img
                    alt={t(item.altKey)}
                    className="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500"
                    src={item.image}
                  />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                  <h3 className="text-xl font-semibold mb-4">
                    {t(item.titleKey)}
                  </h3>
                  <div className="flex items-center gap-3 opacity-0 group-hover:opacity-100 transition-all duration-300 delay-100">
                    <button className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 border border-white/30 hover:border-white/50 rounded-full px-4 py-2.5 text-sm font-medium text-white transition-all duration-300 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl group/button">
                      <Eye className="w-4 h-4 opacity-80" />
                      <span>{t("hotelGallery.exploreMore")}</span>
                      <ArrowRight className="w-4 h-4 transform group-hover/button:translate-x-1 transition-transform duration-300" />
                    </button>
                    <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HotelGallery;
