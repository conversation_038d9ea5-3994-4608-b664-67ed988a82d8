import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { DayPicker } from "react-day-picker";
import { cn } from "@/lib/utils";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return (
    <div className="calendar-modern" style={{ fontSize: "14px" }}>
      <DayPicker
        showOutsideDays={showOutsideDays}
        className={cn("p-4 rounded-xl shadow-lg", className)}
        style={
          {
            "--rdp-cell-size": "40px",
            "--rdp-accent-color": "#2563eb",
            "--rdp-background-color": "#ffffff",
          } as React.CSSProperties
        }
        disabled={(date) => {
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          // Always disable dates before today
          if (date < today) {
            return true;
          }

          // If there's a custom disabled prop, apply it
          if (props.disabled) {
            if (typeof props.disabled === "function") {
              return props.disabled(date);
            } else if (Array.isArray(props.disabled)) {
              return props.disabled.some(
                (disabledDate) => disabledDate.getTime() === date.getTime()
              );
            }
          }

          return false;
        }}
        classNames={{
          months:
            "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
          month: "space-y-4",
          caption: "flex justify-center pt-2 relative items-center mb-4",
          caption_label: "text-lg font-bold text-gray-900",
          nav: "space-x-2 flex items-center",
          nav_button:
            "h-9 w-9 bg-transparent p-0 opacity-80 hover:opacity-100 hover:bg-blue-50 rounded-full transition-all flex items-center justify-center",
          nav_button_previous: "absolute left-1",
          nav_button_next: "absolute right-1",
          table: "w-full border-collapse",
          head_row: "grid grid-cols-7 gap-2 mb-2",
          head_cell:
            "text-gray-500 rounded-md w-10 h-10 font-medium text-xs flex items-center justify-center uppercase tracking-wider",
          row: "grid grid-cols-7 gap-2 mb-2",
          cell: "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 w-10 h-10 flex items-center justify-center",
          day: "h-10 w-10 p-0 font-normal rounded-full hover:bg-blue-50 hover:text-blue-900 transition-colors aria-selected:opacity-100 flex items-center justify-center text-sm",
          day_range_end: "day-range-end",
          day_selected:
            "bg-blue-600 text-white hover:bg-blue-700 hover:text-white focus:bg-blue-600 focus:text-white font-medium",
          day_today:
            "bg-blue-100 text-blue-900 font-semibold ring-2 ring-blue-400 ring-offset-1",
          day_outside: "text-gray-400 opacity-50",
          day_disabled:
            "text-gray-300 opacity-40 cursor-not-allowed hover:bg-transparent",
          day_range_middle:
            "aria-selected:bg-blue-100 aria-selected:text-blue-900",
          day_hidden: "invisible",
          ...classNames,
        }}
        components={{
          IconLeft: ({ ...props }) => (
            <ChevronLeft className="h-5 w-5 text-blue-600" {...props} />
          ),
          IconRight: ({ ...props }) => (
            <ChevronRight className="h-5 w-5 text-blue-600" {...props} />
          ),
        }}
        {...props}
      />
    </div>
  );
}

Calendar.displayName = "Calendar";

export { Calendar };
