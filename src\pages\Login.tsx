import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, Link, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  Eye,
  EyeOff,
  Mail,
  Lock,
  User,
  ArrowLeft,
  Building2,
  Calendar as CalendarIcon,
  Globe,
  Upload,
} from "lucide-react";
import logoImage from "@/assets/logo.png";

const Login = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { login, register, isLoading, isAuthenticated } = useAuth();
  const { isRTL } = useLanguage();

  const [activeTab, setActiveTab] = useState("login");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Login form state
  const [loginForm, setLoginForm] = useState({
    email: "",
    password: "",
  });

  // Register form state
  const [registerForm, setRegisterForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    dateOfBirth: "",
    nationality: "",
    identityDocuments: [] as File[],
  });

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    if (!loginForm.email || !loginForm.password) {
      setError(t("auth.fillAllFields"));
      return;
    }

    const result = await login(loginForm.email, loginForm.password);

    if (result.success) {
      setSuccess(t("auth.loginSuccessful"));
      const from = location.state?.from?.pathname || "/";
      setTimeout(() => navigate(from, { replace: true }), 1500);
    } else {
      setError(result.error || t("auth.loginFailed"));
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    if (
      !registerForm.firstName ||
      !registerForm.lastName ||
      !registerForm.email ||
      !registerForm.password ||
      !registerForm.confirmPassword ||
      !registerForm.dateOfBirth ||
      !registerForm.nationality
    ) {
      setError(t("auth.fillAllFields"));
      return;
    }

    if (registerForm.password !== registerForm.confirmPassword) {
      setError(t("auth.passwordsDontMatch"));
      return;
    }

    if (registerForm.password.length < 6) {
      setError(t("auth.passwordTooShort"));
      return;
    }

    const result = await register(
      registerForm.email,
      registerForm.password,
      registerForm.firstName,
      registerForm.lastName,
      registerForm.dateOfBirth,
      registerForm.nationality,
      registerForm.identityDocuments
    );

    if (result.success) {
      setSuccess(t("auth.registrationSuccessful"));
      const from = location.state?.from?.pathname || "/";
      setTimeout(() => navigate(from, { replace: true }), 1500);
    } else {
      setError(result.error || t("auth.registrationFailed"));
    }
  };

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || "/";
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  const handleInputChange = (
    form: "login" | "register",
    field: string,
    value: string | File[]
  ) => {
    if (form === "login") {
      setLoginForm((prev) => ({ ...prev, [field]: value as string }));
    } else {
      setRegisterForm((prev) => ({ ...prev, [field]: value }));
    }
  };

  const handleFileUpload = (files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);
    const validFiles = fileArray.filter((file) => {
      const validTypes = [
        "application/pdf",
        "image/jpeg",
        "image/jpg",
        "image/png",
      ];
      const maxSize = 5 * 1024 * 1024; // 5MB

      if (!validTypes.includes(file.type)) {
        setError(t("auth.fileTypeError"));
        return false;
      }

      if (file.size > maxSize) {
        setError(t("auth.fileSizeError"));
        return false;
      }

      return true;
    });

    if (validFiles.length > 0) {
      setError("");
      setRegisterForm((prev) => ({ ...prev, identityDocuments: validFiles }));
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-cyan-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Back to Home */}
        <div className="mb-6">
          <Link to="/">
            <Button
              variant="ghost"
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
              {t("auth.backToHome")}
            </Button>
          </Link>
        </div>

        {/* Logo and Title */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <img
              src={logoImage}
              alt="Hala Logo"
              className="w-16 h-16 object-contain"
            />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {t("auth.welcome")}
          </h1>
          <p className="text-gray-600">{t("auth.signInSignUp")}</p>
        </div>

        {/* Auth Card */}
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="login">{t("auth.signIn")}</TabsTrigger>
                <TabsTrigger value="register">{t("auth.signUp")}</TabsTrigger>
              </TabsList>

              {/* Error/Success Messages */}
              {error && (
                <Alert variant="destructive" className="mt-4">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              {success && (
                <Alert className="mt-4 border-green-200 bg-green-50">
                  <AlertDescription className="text-green-800">
                    {success}
                  </AlertDescription>
                </Alert>
              )}

              {/* Login Form */}
              <TabsContent value="login" className="mt-6">
                <form onSubmit={handleLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="login-email">{t("auth.email")}</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="login-email"
                        type="email"
                        placeholder={t("auth.enterEmail")}
                        value={loginForm.email}
                        onChange={(e) =>
                          handleInputChange("login", "email", e.target.value)
                        }
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="login-password">{t("auth.password")}</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="login-password"
                        type={showPassword ? "text" : "password"}
                        placeholder={t("auth.enterPassword")}
                        value={loginForm.password}
                        onChange={(e) =>
                          handleInputChange("login", "password", e.target.value)
                        }
                        className="pl-10 pr-10"
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="remember"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <Label
                        htmlFor="remember"
                        className="text-sm text-gray-600"
                      >
                        {t("auth.rememberMe")}
                      </Label>
                    </div>
                    <Button
                      variant="link"
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      {t("auth.forgotPassword")}
                    </Button>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-[#279fc7] hover:bg-[#279fc7]/90 text-white"
                    disabled={isLoading}
                  >
                    {isLoading ? t("auth.signingIn") : t("auth.signIn")}
                  </Button>
                </form>

                <div className="mt-6">
                  <Separator className="my-4" />
                  <div className="text-center">
                    <p className="text-sm text-gray-600">
                      {t("auth.demoCredentials")}
                    </p>
                  </div>
                </div>
              </TabsContent>

              {/* Register Form */}
              <TabsContent value="register" className="mt-6">
                <form onSubmit={handleRegister} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="register-firstName">
                        {t("auth.firstName")}
                      </Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="register-firstName"
                          type="text"
                          placeholder={t("auth.enterFirstName")}
                          value={registerForm.firstName}
                          onChange={(e) =>
                            handleInputChange(
                              "register",
                              "firstName",
                              e.target.value
                            )
                          }
                          className="pl-10"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="register-lastName">
                        {t("auth.lastName")}
                      </Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="register-lastName"
                          type="text"
                          placeholder={t("auth.enterLastName")}
                          value={registerForm.lastName}
                          onChange={(e) =>
                            handleInputChange(
                              "register",
                              "lastName",
                              e.target.value
                            )
                          }
                          className="pl-10"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="register-email">{t("auth.email")}</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="register-email"
                        type="email"
                        placeholder={t("auth.enterEmail")}
                        value={registerForm.email}
                        onChange={(e) =>
                          handleInputChange("register", "email", e.target.value)
                        }
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="register-dateOfBirth">
                        {t("auth.dateOfBirth")}
                      </Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <div className="relative">
                            <CalendarIcon className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              id="register-dateOfBirth"
                              placeholder={t("auth.selectDateOfBirth")}
                              value={registerForm.dateOfBirth}
                              readOnly
                              className="pl-10 cursor-pointer"
                              required
                            />
                          </div>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={
                              registerForm.dateOfBirth
                                ? new Date(registerForm.dateOfBirth)
                                : undefined
                            }
                            onSelect={(date) => {
                              if (date) {
                                handleInputChange(
                                  "register",
                                  "dateOfBirth",
                                  date.toISOString().split("T")[0]
                                );
                              }
                            }}
                            disabled={(date) =>
                              date > new Date() || date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="register-nationality">
                        {t("auth.nationality")}
                      </Label>
                      <Select
                        value={registerForm.nationality}
                        onValueChange={(value) =>
                          handleInputChange("register", "nationality", value)
                        }
                      >
                        <SelectTrigger className="pl-10">
                          <Globe className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <SelectValue
                            placeholder={t("auth.selectNationality")}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="saudi">Saudi Arabia</SelectItem>
                          <SelectItem value="uae">
                            United Arab Emirates
                          </SelectItem>
                          <SelectItem value="kuwait">Kuwait</SelectItem>
                          <SelectItem value="qatar">Qatar</SelectItem>
                          <SelectItem value="bahrain">Bahrain</SelectItem>
                          <SelectItem value="oman">Oman</SelectItem>
                          <SelectItem value="jordan">Jordan</SelectItem>
                          <SelectItem value="lebanon">Lebanon</SelectItem>
                          <SelectItem value="egypt">Egypt</SelectItem>
                          <SelectItem value="usa">United States</SelectItem>
                          <SelectItem value="uk">United Kingdom</SelectItem>
                          <SelectItem value="canada">Canada</SelectItem>
                          <SelectItem value="australia">Australia</SelectItem>
                          <SelectItem value="germany">Germany</SelectItem>
                          <SelectItem value="france">France</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="register-password">
                      {t("auth.password")}
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="register-password"
                        type={showPassword ? "text" : "password"}
                        placeholder={t("auth.createPassword")}
                        value={registerForm.password}
                        onChange={(e) =>
                          handleInputChange(
                            "register",
                            "password",
                            e.target.value
                          )
                        }
                        className="pl-10 pr-10"
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="register-confirm-password">
                      {t("auth.confirmPassword")}
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="register-confirm-password"
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder={t("auth.confirmYourPassword")}
                        value={registerForm.confirmPassword}
                        onChange={(e) =>
                          handleInputChange(
                            "register",
                            "confirmPassword",
                            e.target.value
                          )
                        }
                        className="pl-10 pr-10"
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label htmlFor="register-identity-documents">
                      {t("auth.identityDocuments")}
                    </Label>

                    {/* File Upload Area */}
                    <div className="relative">
                      <Input
                        id="register-identity-documents"
                        type="file"
                        multiple
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={(e) => handleFileUpload(e.target.files)}
                        className="hidden"
                      />

                      <label
                        htmlFor="register-identity-documents"
                        className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors duration-200"
                      >
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <Upload className="w-8 h-8 mb-2 text-gray-400" />
                          <p className="mb-2 text-sm text-gray-500">
                            <span className="font-semibold">
                              {t("auth.clickToUpload")}
                            </span>{" "}
                            {t("auth.orDragAndDrop")}
                          </p>
                          <p className="text-xs text-gray-500">
                            {t("auth.fileTypesAccepted")}
                          </p>
                        </div>
                      </label>
                    </div>

                    {/* File List */}
                    {registerForm.identityDocuments.length > 0 && (
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-gray-700">
                          {t("auth.uploadedFiles")} (
                          {registerForm.identityDocuments.length})
                        </p>
                        <div className="space-y-2 max-h-32 overflow-y-auto">
                          {registerForm.identityDocuments.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg"
                            >
                              <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0">
                                  {file.type === "application/pdf" ? (
                                    <svg
                                      className="w-5 h-5 text-red-500"
                                      fill="currentColor"
                                      viewBox="0 0 20 20"
                                    >
                                      <path
                                        fillRule="evenodd"
                                        d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                                        clipRule="evenodd"
                                      />
                                    </svg>
                                  ) : (
                                    <svg
                                      className="w-5 h-5 text-blue-500"
                                      fill="currentColor"
                                      viewBox="0 0 20 20"
                                    >
                                      <path
                                        fillRule="evenodd"
                                        d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                                        clipRule="evenodd"
                                      />
                                    </svg>
                                  )}
                                </div>
                                <div className="min-w-0 flex-1">
                                  <p className="text-sm font-medium text-gray-900 truncate">
                                    {file.name}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    {(file.size / 1024 / 1024).toFixed(2)} MB
                                  </p>
                                </div>
                              </div>
                              <div className="flex-shrink-0">
                                <div className="flex items-center space-x-1">
                                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                  <span className="text-xs text-green-600 font-medium">
                                    {t("auth.uploaded")}
                                  </span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Help Text */}
                    <div className="text-xs text-gray-500 bg-blue-50 p-3 rounded-lg">
                      <div className="flex items-start space-x-2">
                        <svg
                          className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <div>
                          <p className="font-medium text-blue-700 mb-1">
                            {t("auth.acceptedFileTypes")}
                          </p>
                          <ul className="space-y-1 text-blue-600">
                            <li>{t("auth.passportOrNationalId")}</li>
                            <li>{t("auth.driversLicense")}</li>
                            <li>{t("auth.otherGovernmentId")}</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="terms"
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      required
                    />
                    <Label htmlFor="terms" className="text-sm text-gray-600">
                      {t("auth.agreeToTerms")}{" "}
                      <Button
                        variant="link"
                        className="p-0 h-auto text-sm text-blue-600 hover:text-blue-800"
                      >
                        {t("auth.termsOfService")}
                      </Button>{" "}
                      {t("auth.and")}{" "}
                      <Button
                        variant="link"
                        className="p-0 h-auto text-sm text-blue-600 hover:text-blue-800"
                      >
                        {t("auth.privacyPolicy")}
                      </Button>
                    </Label>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-[#279fc7] hover:bg-[#279fc7]/90 text-white"
                    disabled={isLoading}
                  >
                    {isLoading
                      ? t("auth.creatingAccount")
                      : t("auth.createAccount")}
                  </Button>
                </form>
              </TabsContent>
            </Tabs>
          </CardHeader>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-600">
            {t("auth.byContinuing")}{" "}
            <Button
              variant="link"
              className="p-0 h-auto text-sm text-blue-600 hover:text-blue-800"
            >
              {t("auth.termsOfService")}
            </Button>{" "}
            {t("auth.and")}{" "}
            <Button
              variant="link"
              className="p-0 h-auto text-sm text-blue-600 hover:text-blue-800"
            >
              {t("auth.privacyPolicy")}
            </Button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
